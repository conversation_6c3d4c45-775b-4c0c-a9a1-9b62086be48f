#!/usr/bin/env python3
"""
Script mejorado para seguir el link de Pokémon con mejor manejo de sesión
"""

import requests
import json
from datetime import datetime
from bs4 import BeautifulSoup

class PokemonLinkMejorado:
    def __init__(self):
        # Crear nueva sesión limpia
        self.session = requests.Session()
        
        # Headers más completos para simular navegador real
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
        
        # Link del correo (CORREGIDO - faltaba la parte final)
        self.pokemon_link = "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/49d9fd87c3028b1267f9825fddf6e825"
        
        print(f"🔗 SEGUIDOR DE LINK POKÉMON MEJORADO")
        print(f"🌐 Link: {self.pokemon_link}")

    def acceder_link_directo(self):
        """Accede directamente al link sin autenticación previa"""
        print(f"\n🔗 ACCESO DIRECTO AL LINK")
        print("=" * 40)
        
        try:
            print(f"📡 Accediendo directamente al link...")
            
            # Hacer request directo
            response = self.session.get(self.pokemon_link, allow_redirects=True)
            
            print(f"📥 Status: {response.status_code}")
            print(f"🌐 URL final: {response.url}")
            print(f"📄 Tamaño: {len(response.text)} caracteres")
            
            # Mostrar headers de respuesta importantes
            print(f"📋 Headers importantes:")
            important_headers = ['content-type', 'set-cookie', 'location', 'cache-control']
            for header in important_headers:
                if header in response.headers:
                    value = response.headers[header]
                    if len(value) > 100:
                        value = value[:100] + "..."
                    print(f"   {header}: {value}")
            
            # Guardar respuesta
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pokemon_directo_{timestamp}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"💾 Respuesta guardada: {filename}")
            
            return response.text, response.url, response.status_code
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return None, None, None

    def acceder_con_referer(self):
        """Accede al link simulando que viene desde un email"""
        print(f"\n📧 ACCESO CON REFERER DE EMAIL")
        print("=" * 40)
        
        try:
            # Simular que venimos desde un cliente de email
            headers_email = {
                'Referer': 'https://mail.google.com/',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Dest': 'document'
            }
            
            print(f"📡 Accediendo con referer de email...")
            
            response = self.session.get(
                self.pokemon_link, 
                headers=headers_email,
                allow_redirects=True
            )
            
            print(f"📥 Status: {response.status_code}")
            print(f"🌐 URL final: {response.url}")
            print(f"📄 Tamaño: {len(response.text)} caracteres")
            
            # Guardar respuesta
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pokemon_email_referer_{timestamp}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"💾 Respuesta guardada: {filename}")
            
            return response.text, response.url, response.status_code
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return None, None, None

    def acceder_paso_a_paso(self):
        """Accede paso a paso simulando navegación real"""
        print(f"\n👣 ACCESO PASO A PASO")
        print("=" * 30)
        
        try:
            # Paso 1: Ir a la página principal de Pokémon
            print(f"1️⃣ Accediendo a página principal...")
            home_response = self.session.get("https://www.pokemon.com/us/")
            print(f"   Status: {home_response.status_code}")
            
            # Paso 2: Ir a la página de login
            print(f"2️⃣ Accediendo a página de login...")
            login_response = self.session.get("https://club.pokemon.com/us/pokemon-trainer-club/login")
            print(f"   Status: {login_response.status_code}")
            
            # Paso 3: Ahora acceder al link de confirmación
            print(f"3️⃣ Accediendo al link de confirmación...")
            
            # Headers como si viniéramos del sitio de Pokémon
            headers_pokemon = {
                'Referer': 'https://club.pokemon.com/us/pokemon-trainer-club/login',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Dest': 'document'
            }
            
            response = self.session.get(
                self.pokemon_link,
                headers=headers_pokemon,
                allow_redirects=True
            )
            
            print(f"📥 Status: {response.status_code}")
            print(f"🌐 URL final: {response.url}")
            print(f"📄 Tamaño: {len(response.text)} caracteres")
            
            # Guardar respuesta
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pokemon_paso_a_paso_{timestamp}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"💾 Respuesta guardada: {filename}")
            
            return response.text, response.url, response.status_code
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return None, None, None

    def analizar_respuesta(self, html_content, url, status_code):
        """Analiza la respuesta para ver qué contiene"""
        print(f"\n🔍 ANÁLISIS DE RESPUESTA")
        print("=" * 30)
        
        if not html_content:
            print("❌ No hay contenido para analizar")
            return None
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Buscar elementos específicos de la página de confirmación
        print(f"🔍 Buscando elementos de confirmación...")
        
        # 1. Buscar título específico
        title = soup.find('title')
        if title:
            print(f"📄 Título: {title.get_text(strip=True)}")
        
        # 2. Buscar texto "Approve Your Email Change"
        approve_text = soup.find(string=lambda text: text and 'approve your email change' in text.lower())
        if approve_text:
            print(f"✅ Texto de aprobación encontrado: {approve_text.strip()}")
        
        # 3. Buscar botones de Approve/Reject
        approve_button = soup.find('button', string=lambda text: text and 'approve' in text.lower())
        reject_button = soup.find('button', string=lambda text: text and 'reject' in text.lower())
        
        if approve_button:
            print(f"✅ Botón Approve encontrado: {approve_button.get_text(strip=True)}")
        if reject_button:
            print(f"✅ Botón Reject encontrado: {reject_button.get_text(strip=True)}")
        
        # 4. Buscar campos de email
        email_inputs = soup.find_all('input', type='email')
        text_inputs = soup.find_all('input', type='text')
        
        print(f"📧 Campos de email encontrados: {len(email_inputs)}")
        print(f"📝 Campos de texto encontrados: {len(text_inputs)}")
        
        # 5. Buscar campos de password
        password_inputs = soup.find_all('input', type='password')
        print(f"🔐 Campos de password encontrados: {len(password_inputs)}")
        
        # 6. Buscar formularios
        forms = soup.find_all('form')
        print(f"📋 Formularios encontrados: {len(forms)}")
        
        for i, form in enumerate(forms):
            action = form.get('action', '')
            method = form.get('method', 'GET')
            print(f"   Formulario {i+1}: {method} -> {action}")
        
        # 7. Buscar texto específico de emails
        email_patterns = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '@1em0nstore.trade',
            '@dujaw.com'
        ]
        
        for pattern in email_patterns:
            if pattern.lower() in html_content.lower():
                print(f"📧 Email encontrado: {pattern}")
        
        # 8. Buscar mensajes de error o expiración
        error_keywords = ['invalid', 'expired', 'timed out', 'error']
        for keyword in error_keywords:
            if keyword.lower() in html_content.lower():
                print(f"⚠️ Palabra clave de error encontrada: {keyword}")
        
        # 9. Verificar si es la página correcta
        confirmation_indicators = [
            'approve your email change',
            'email change approval',
            'current email address',
            'new email address'
        ]
        
        indicators_found = 0
        for indicator in confirmation_indicators:
            if indicator.lower() in html_content.lower():
                indicators_found += 1
                print(f"✅ Indicador encontrado: {indicator}")
        
        if indicators_found >= 2:
            print(f"🎉 ¡Página de confirmación detectada! ({indicators_found}/4 indicadores)")
            return True
        else:
            print(f"❌ No parece ser la página de confirmación ({indicators_found}/4 indicadores)")
            return False

    def probar_todos_los_metodos(self):
        """Prueba todos los métodos de acceso"""
        print(f"\n🚀 PROBANDO TODOS LOS MÉTODOS DE ACCESO")
        print("=" * 50)
        
        metodos = [
            ("Acceso Directo", self.acceder_link_directo),
            ("Con Referer Email", self.acceder_con_referer),
            ("Paso a Paso", self.acceder_paso_a_paso)
        ]
        
        for nombre, metodo in metodos:
            print(f"\n{'='*60}")
            print(f"🔄 MÉTODO: {nombre.upper()}")
            print(f"{'='*60}")
            
            html_content, url, status_code = metodo()
            
            if html_content:
                es_pagina_correcta = self.analizar_respuesta(html_content, url, status_code)
                
                if es_pagina_correcta:
                    print(f"\n🎉 ¡ÉXITO! Método '{nombre}' funcionó correctamente")
                    return html_content, url, status_code
                else:
                    print(f"\n⚠️ Método '{nombre}' no obtuvo la página correcta")
            else:
                print(f"\n❌ Método '{nombre}' falló")
        
        print(f"\n😞 Ningún método obtuvo la página de confirmación correcta")
        return None, None, None

def main():
    """Función principal"""
    print("🔗 SEGUIDOR DE LINK POKÉMON MEJORADO")
    print("=" * 50)
    
    try:
        follower = PokemonLinkMejorado()
        html_content, url, status_code = follower.probar_todos_los_metodos()
        
        if html_content:
            print(f"\n✅ Proceso completado exitosamente")
            print(f"🌐 URL final: {url}")
            print(f"📊 Status: {status_code}")
        else:
            print(f"\n❌ No se pudo acceder a la página de confirmación")
            print(f"💡 Posibles causas:")
            print(f"   - El link ha expirado")
            print(f"   - Se requiere autenticación específica")
            print(f"   - El token en el link no es válido")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
