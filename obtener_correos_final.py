#!/usr/bin/env python3
"""
Script final para obtener correos usando Livewire
Basado en el análisis exitoso del mailbox autenticado
"""

import requests
import json
import time
import re
import html
from datetime import datetime
from bs4 import BeautifulSoup

class DujawCorreosFinal:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Credenciales correctas
        self.email = "<EMAIL>"  # Nota: cordell, no coRdeLL
        self.dujaw_password = "unlockgs2024"  # Password de Dujaw
        self.confirmation_password = "EMVaB#6G3"  # Password de confirmación
        
        self.base_url = "https://dujaw.com"
        self.mailbox_url = f"https://dujaw.com/mailbox/{self.email}"
        self.unlock_url = "https://dujaw.com/unlock"
        self.csrf_token = None
        self.is_authenticated = False
        self.livewire_data = None
        
        print(f"🔧 Configuración:")
        print(f"   📧 Email: {self.email}")
        print(f"   🔐 Password Dujaw: {self.dujaw_password}")
        print(f"   🔑 Password Confirmación: {self.confirmation_password}")

    def unlock_mailbox(self):
        """Hace unlock del mailbox con el password correcto"""
        print(f"\n🔓 UNLOCK DEL MAILBOX")
        print("=" * 30)
        
        # Obtener página inicial
        response = self.session.get(self.mailbox_url)
        if response.status_code != 200:
            print(f"❌ Error accediendo mailbox: {response.status_code}")
            return False
        
        # Obtener token CSRF
        soup = BeautifulSoup(response.text, 'html.parser')
        csrf_input = soup.find('input', {'name': '_token'})
        if not csrf_input:
            print("❌ No se encontró token CSRF")
            return False
        
        self.csrf_token = csrf_input.get('value')
        print(f"✅ Token CSRF obtenido: {self.csrf_token[:20]}...")
        
        # Enviar formulario de unlock
        form_data = {
            '_token': self.csrf_token,
            'password': self.dujaw_password
        }
        
        response = self.session.post(self.unlock_url, data=form_data, allow_redirects=True)
        
        if response.status_code == 200:
            # Verificar si el unlock fue exitoso
            if 'unlock' not in response.text.lower():
                print("✅ Unlock exitoso!")
                self.is_authenticated = True
                return True
            else:
                print("❌ Unlock falló - aún se pide password")
                return False
        else:
            print(f"❌ Error en unlock: {response.status_code}")
            return False

    def extraer_datos_livewire(self):
        """Extrae los datos del componente Livewire frontend.app"""
        print(f"\n🧩 EXTRAYENDO DATOS LIVEWIRE")
        print("=" * 35)
        
        if not self.is_authenticated:
            print("⚠️ No autenticado")
            return False
        
        # Obtener página del mailbox autenticado
        response = self.session.get(self.mailbox_url)
        if response.status_code != 200:
            print(f"❌ Error accediendo mailbox: {response.status_code}")
            return False
        
        html_content = response.text
        
        # Buscar el componente frontend.app
        pattern = r'wire:id="([^"]+)"\s+wire:initial-data="([^"]+)"[^>]*'
        
        for match in re.finditer(pattern, html_content):
            wire_id = match.group(1)
            initial_data_encoded = match.group(2)
            
            # Decodificar HTML entities
            initial_data_json = html.unescape(initial_data_encoded)
            
            try:
                initial_data = json.loads(initial_data_json)
                
                # Verificar si es el componente frontend.app
                if initial_data.get('fingerprint', {}).get('name') == 'frontend.app':
                    print(f"✅ Componente frontend.app encontrado")
                    print(f"   Wire ID: {wire_id}")
                    print(f"   Email: {initial_data['serverMemo']['data']['email']}")
                    print(f"   Mensajes actuales: {len(initial_data['serverMemo']['data']['messages'])}")
                    
                    self.livewire_data = {
                        'wire_id': wire_id,
                        'initial_data': initial_data
                    }
                    return True
                    
            except json.JSONDecodeError as e:
                print(f"❌ Error decodificando JSON: {e}")
                continue
        
        print("❌ No se encontró el componente frontend.app")
        return False

    def hacer_llamada_livewire(self, event_name, params=None):
        """Hace una llamada a Livewire"""
        if not self.livewire_data:
            print("❌ No hay datos de Livewire")
            return None
        
        wire_id = self.livewire_data['wire_id']
        initial_data = self.livewire_data['initial_data']
        
        # Construir la petición Livewire
        livewire_request = {
            'fingerprint': initial_data['fingerprint'],
            'serverMemo': initial_data['serverMemo'],
            'updates': [
                {
                    'type': 'fireEvent',
                    'payload': {
                        'id': wire_id,
                        'event': event_name,
                        'params': params or []
                    }
                }
            ]
        }
        
        # Headers para Livewire
        headers = {
            'X-Livewire': 'true',
            'X-CSRF-TOKEN': self.csrf_token,
            'Content-Type': 'application/json',
            'Accept': 'text/html, application/xhtml+xml',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': self.mailbox_url
        }
        
        # URL de Livewire
        livewire_url = f"{self.base_url}/livewire/message/{initial_data['fingerprint']['name']}"
        
        try:
            print(f"🔄 Llamada Livewire: {event_name}")
            response = self.session.post(livewire_url, json=livewire_request, headers=headers)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ Respuesta exitosa")
                    
                    # Actualizar datos del servidor si están disponibles
                    if 'serverMemo' in data:
                        self.livewire_data['initial_data']['serverMemo'] = data['serverMemo']
                    
                    return data
                    
                except json.JSONDecodeError:
                    print(f"⚠️ Respuesta no es JSON")
                    return response.text
            else:
                print(f"❌ Error: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error en llamada: {e}")
            return None

    def obtener_correos(self):
        """Proceso completo para obtener correos"""
        print(f"\n📧 OBTENIENDO CORREOS COMPLETO")
        print("=" * 40)
        
        # Paso 1: Unlock
        if not self.unlock_mailbox():
            return None
        
        # Paso 2: Extraer datos Livewire
        if not self.extraer_datos_livewire():
            return None
        
        # Paso 3: Sincronizar email
        print(f"\n1️⃣ Sincronizando email...")
        sync_response = self.hacer_llamada_livewire('syncEmail', [self.email])
        
        if sync_response:
            print("✅ syncEmail exitoso")
        else:
            print("❌ syncEmail falló")
        
        # Paso 4: Obtener mensajes (múltiples intentos)
        print(f"\n2️⃣ Obteniendo mensajes...")
        
        mensajes_encontrados = []
        
        for intento in range(5):
            print(f"\n🔄 Intento {intento + 1}/5")
            
            fetch_response = self.hacer_llamada_livewire('fetchMessages')
            
            if fetch_response and isinstance(fetch_response, dict):
                # Verificar si hay mensajes en la respuesta
                if 'serverMemo' in fetch_response and 'data' in fetch_response['serverMemo']:
                    server_data = fetch_response['serverMemo']['data']
                    messages = server_data.get('messages', [])
                    
                    if messages:
                        print(f"🎉 ¡{len(messages)} mensajes encontrados!")
                        mensajes_encontrados.extend(messages)
                        break
                    else:
                        print("📭 No hay mensajes aún")
                
                # Verificar si hay HTML actualizado
                if 'effects' in fetch_response and 'html' in fetch_response['effects']:
                    html_content = fetch_response['effects']['html']
                    if html_content and len(html_content) > 100:
                        print(f"📄 HTML actualizado recibido ({len(html_content)} chars)")
                        # Aquí podrías parsear el HTML para buscar mensajes
            
            # Esperar entre intentos
            if intento < 4:
                print("⏳ Esperando 3 segundos...")
                time.sleep(3)
        
        # Resultado final
        resultado = {
            'timestamp': datetime.now().isoformat(),
            'email': self.email,
            'mensajes': mensajes_encontrados,
            'total_mensajes': len(mensajes_encontrados),
            'livewire_data': self.livewire_data
        }
        
        return resultado

    def mostrar_resultados(self, resultado):
        """Muestra los resultados obtenidos"""
        print(f"\n📊 RESULTADOS FINALES")
        print("=" * 30)
        
        if not resultado:
            print("❌ No se obtuvieron resultados")
            return
        
        print(f"📧 Email: {resultado['email']}")
        print(f"📬 Total mensajes: {resultado['total_mensajes']}")
        print(f"⏰ Timestamp: {resultado['timestamp']}")
        
        if resultado['mensajes']:
            print(f"\n📬 MENSAJES ENCONTRADOS:")
            for i, mensaje in enumerate(resultado['mensajes'], 1):
                print(f"\n{i}. Mensaje:")
                if isinstance(mensaje, dict):
                    for key, value in mensaje.items():
                        print(f"   {key}: {value}")
                else:
                    print(f"   {mensaje}")
        else:
            print(f"\n📭 No se encontraron mensajes")
            print(f"💡 Posibles razones:")
            print(f"   - El buzón está vacío")
            print(f"   - Los mensajes se eliminaron automáticamente")
            print(f"   - Necesita más tiempo para cargar")
        
        # Guardar resultados
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"correos_finales_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(resultado, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Resultados guardados en: {filename}")

def main():
    """Función principal"""
    print("🚀 OBTENER CORREOS - VERSIÓN FINAL")
    print("=" * 50)
    
    try:
        # Crear instancia
        dujaw = DujawCorreosFinal()
        
        # Obtener correos
        resultado = dujaw.obtener_correos()
        
        # Mostrar resultados
        dujaw.mostrar_resultados(resultado)
        
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🏁 Proceso completado")

if __name__ == "__main__":
    main()
