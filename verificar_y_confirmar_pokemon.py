#!/usr/bin/env python3
"""
Script para verificar el estado de un link de Pokémon y confirmarlo si es necesario
"""

import requests
import time
from datetime import datetime
from bs4 import BeautifulSoup

def verificar_estado_link(link):
    """Verifica el estado actual de un link de confirmación"""
    print(f"🔍 Verificando estado del link...")
    print(f"🔗 {link}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    try:
        response = session.get(link)
        print(f"📥 Status: {response.status_code}")
        print(f"🌐 URL: {response.url}")
        
        if response.status_code != 200:
            return "ERROR", f"HTTP {response.status_code}", session
        
        html_lower = response.text.lower()
        
        # Verificar si ya está activado
        if 'your account has already been activated' in html_lower:
            print(f"✅ Estado: YA ACTIVADO")
            return "ALREADY_ACTIVATED", response.text, session
        
        # Verificar si está expirado
        elif 'expired' in html_lower or 'no longer valid' in html_lower:
            print(f"❌ Estado: EXPIRADO")
            return "EXPIRED", response.text, session
        
        # Verificar si hay formulario activo
        soup = BeautifulSoup(response.text, 'html.parser')
        password_form = soup.find('input', {'type': 'password'})
        
        if password_form:
            print(f"📋 Estado: FORMULARIO ACTIVO - Requiere confirmación")
            return "NEEDS_CONFIRMATION", response.text, session
        
        # Verificar otros estados
        elif 'could not be completed' in html_lower:
            print(f"⚠️ Estado: ERROR - Could not be completed")
            return "ERROR", response.text, session
        
        else:
            print(f"❓ Estado: DESCONOCIDO")
            return "UNKNOWN", response.text, session
    
    except Exception as e:
        print(f"❌ Error verificando link: {e}")
        return "EXCEPTION", str(e), session

def confirmar_link_pokemon(link, password, session):
    """Confirma un link de Pokémon si es necesario"""
    print(f"\n🔐 Confirmando link...")
    print(f"🔑 Password: {password}")
    
    try:
        # Acceder nuevamente para obtener token fresco
        response = session.get(link)
        
        if response.status_code != 200:
            print(f"❌ Error accediendo: {response.status_code}")
            return False
        
        # Parsear formulario
        soup = BeautifulSoup(response.text, 'html.parser')
        form = None
        
        for f in soup.find_all('form'):
            if f.find('input', {'type': 'password'}):
                form = f
                break
        
        if not form:
            print(f"❌ No se encontró formulario")
            return False
        
        # Obtener token CSRF
        csrf_token = None
        csrf_input = form.find('input', {'name': 'csrfmiddlewaretoken'})
        if csrf_input:
            csrf_token = csrf_input.get('value')
            print(f"🔒 Token CSRF: {csrf_token[:20]}...")
        
        # Preparar datos
        form_data = {}
        
        for input_elem in form.find_all('input'):
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            input_type = input_elem.get('type', 'text')
            
            if not name:
                continue
            
            if input_type == 'password':
                form_data[name] = password
                print(f"🔑 Password configurado: {name}")
            else:
                form_data[name] = value
                if input_type == 'email' and value:
                    print(f"📧 {name}: {value}")
                elif input_type == 'submit' and value:
                    print(f"🔘 {name}: {value}")
        
        # Configurar headers
        session.headers.update({
            'Referer': link,
            'Origin': 'https://club.pokemon.com',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
        
        if csrf_token:
            session.headers['X-CSRFToken'] = csrf_token
        
        # Enviar confirmación
        print(f"🚀 Enviando confirmación...")
        time.sleep(1)
        
        confirm_response = session.post(link, data=form_data, allow_redirects=True)
        
        print(f"📥 Status: {confirm_response.status_code}")
        print(f"🌐 URL final: {confirm_response.url}")
        
        # Guardar respuesta
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"confirmacion_verificada_{timestamp}.html"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"<!-- Link: {link} -->\n")
            f.write(f"<!-- Password: {password} -->\n")
            f.write(f"<!-- Status: {confirm_response.status_code} -->\n\n")
            f.write(confirm_response.text)
        
        print(f"💾 Respuesta: {filename}")
        
        # Analizar resultado
        html_lower = confirm_response.text.lower()
        
        # Verificar redirección exitosa
        if confirm_response.url != link and 'pokemon.com' in confirm_response.url:
            print(f"✅ ¡CONFIRMACIÓN EXITOSA! (Redirección detectada)")
            return True
        
        # Verificar si ahora dice que ya está activado
        elif 'your account has already been activated' in html_lower:
            print(f"✅ ¡CONFIRMACIÓN EXITOSA! (Cuenta ya activada)")
            return True
        
        # Verificar errores
        elif 'could not be completed' in html_lower:
            print(f"⚠️ 'Could not be completed' - Puede ya estar procesado")
            return False
        
        elif 'invalid password' in html_lower:
            print(f"❌ Password incorrecto")
            return False
        
        else:
            print(f"⚠️ Resultado incierto")
            return False
    
    except Exception as e:
        print(f"❌ Error en confirmación: {e}")
        return False

def main():
    """Función principal"""
    print("🔍 VERIFICADOR Y CONFIRMADOR INTELIGENTE DE POKÉMON")
    print("=" * 70)
    
    # Link específico a verificar
    link = "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/6119670bb9719516b9be04350aaa3107"
    password = "!DbCyY&48"  # Cambiar según sea necesario
    
    print(f"🔗 Link: {link}")
    print(f"🔑 Password: {password}")
    
    # 1. Verificar estado actual
    estado, contenido, session = verificar_estado_link(link)
    
    try:
        # 2. Actuar según el estado
        if estado == "ALREADY_ACTIVATED":
            print(f"\n🎉 ¡EL LINK YA ESTÁ ACTIVADO!")
            print(f"✅ No se requiere ninguna acción")
            print(f"📧 El cambio de email ya fue procesado exitosamente")
        
        elif estado == "NEEDS_CONFIRMATION":
            print(f"\n📋 El link requiere confirmación")
            print(f"🔄 Procediendo con la confirmación automática...")
            
            if confirmar_link_pokemon(link, password, session):
                print(f"\n🎉 ¡CONFIRMACIÓN COMPLETADA EXITOSAMENTE!")
                print(f"✅ El cambio de email fue confirmado")
            else:
                print(f"\n❌ La confirmación no fue exitosa")
                print(f"💡 Revisar archivo de respuesta para más detalles")
        
        elif estado == "EXPIRED":
            print(f"\n❌ EL LINK HA EXPIRADO")
            print(f"💡 Necesitas solicitar un nuevo cambio de email")
        
        elif estado == "ERROR":
            print(f"\n❌ ERROR EN EL LINK")
            print(f"💡 El link puede estar dañado o ser inválido")
        
        else:
            print(f"\n❓ ESTADO DESCONOCIDO")
            print(f"💡 Revisar manualmente el link")
        
        # 3. Verificar estado final
        print(f"\n🔍 Verificación final...")
        estado_final, _, _ = verificar_estado_link(link)
        
        if estado_final == "ALREADY_ACTIVATED":
            print(f"🎉 ¡CONFIRMADO! El link ahora muestra 'Your account has already been activated'")
        else:
            print(f"⚠️ Estado final: {estado_final}")
    
    finally:
        session.close()
    
    print(f"\n🏁 Proceso completado")

if __name__ == "__main__":
    main()
