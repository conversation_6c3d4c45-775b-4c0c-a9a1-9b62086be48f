#!/usr/bin/env python3
"""
Enhanced Dujaw API with advanced features
Includes filtering, notifications, webhooks, and improved error handling
"""

import requests
import json
import time
import logging
import smtplib
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from typing import Dict, Any, Optional, List, Callable
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import queue
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dujaw_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MessagePriority(Enum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

@dataclass
class MessageFilter:
    """Configuration for message filtering"""
    sender_patterns: List[str] = None
    subject_patterns: List[str] = None
    content_patterns: List[str] = None
    min_priority: MessagePriority = MessagePriority.LOW
    max_age_hours: int = 24
    exclude_read: bool = False

@dataclass
class NotificationConfig:
    """Configuration for notifications"""
    email_enabled: bool = False
    email_smtp_server: str = "smtp.gmail.com"
    email_smtp_port: int = 587
    email_username: str = ""
    email_password: str = ""
    email_recipients: List[str] = None
    webhook_enabled: bool = False
    webhook_url: str = ""
    webhook_headers: Dict[str, str] = None

@dataclass
class Message:
    """Enhanced message data structure"""
    id: str
    subject: str
    sender: str
    content: str
    html_content: str
    timestamp: datetime
    priority: MessagePriority
    is_read: bool = False
    tags: List[str] = None

class EnhancedDujawAPI:
    def __init__(self, config_file: str = None):
        # Initialize base API
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Configuration
        self.base_url = "https://dujaw.com"
        self.mailbox_url = "https://dujaw.com/mailbox/<EMAIL>"
        self.unlock_url = "https://dujaw.com/unlock"
        self.password = "unlockgs2024"
        self.csrf_token = None
        self.is_authenticated = False
        
        # Enhanced features
        self.message_cache: Dict[str, Message] = {}
        self.filters: List[MessageFilter] = []
        self.notification_config = NotificationConfig()
        self.event_handlers: Dict[str, List[Callable]] = {
            'new_message': [],
            'authentication_failed': [],
            'connection_lost': [],
            'error': []
        }
        
        # Background monitoring
        self.monitoring_active = False
        self.monitoring_thread = None
        self.notification_queue = queue.Queue()
        
        # Load configuration if provided
        if config_file:
            self.load_config(config_file)
        
        logger.info("Enhanced Dujaw API initialized")

    def load_config(self, config_file: str):
        """Load configuration from JSON file"""
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            # Update notification config
            if 'notifications' in config:
                notif_config = config['notifications']
                self.notification_config = NotificationConfig(**notif_config)
            
            # Load filters
            if 'filters' in config:
                for filter_config in config['filters']:
                    self.filters.append(MessageFilter(**filter_config))
            
            logger.info(f"Configuration loaded from {config_file}")
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")

    def add_event_handler(self, event: str, handler: Callable):
        """Add event handler for specific events"""
        if event in self.event_handlers:
            self.event_handlers[event].append(handler)
            logger.info(f"Added handler for event: {event}")

    def trigger_event(self, event: str, data: Any = None):
        """Trigger event handlers"""
        if event in self.event_handlers:
            for handler in self.event_handlers[event]:
                try:
                    handler(data)
                except Exception as e:
                    logger.error(f"Error in event handler for {event}: {e}")

    def get_csrf_token(self) -> Optional[str]:
        """Enhanced CSRF token retrieval with retry logic"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"Obtaining CSRF token (attempt {attempt + 1}/{max_retries})")
                response = self.session.get(self.mailbox_url, timeout=30)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # Multiple token search strategies
                    token_selectors = [
                        ('input[name="_token"]', 'value'),
                        ('meta[name="csrf-token"]', 'content'),
                        ('input[type="hidden"][name="_token"]', 'value')
                    ]
                    
                    for selector, attr in token_selectors:
                        element = soup.select_one(selector)
                        if element and element.get(attr):
                            token = element.get(attr)
                            logger.info(f"CSRF token obtained: {token[:20]}...")
                            return token
                    
                    logger.warning("No CSRF token found in page")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # Exponential backoff
                        continue
                else:
                    logger.error(f"HTTP error getting page: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error obtaining CSRF token: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                    continue
        
        self.trigger_event('error', {'type': 'csrf_token_failed'})
        return None

    def unlock_mailbox(self) -> bool:
        """Enhanced unlock with better error handling"""
        try:
            logger.info("Attempting to unlock mailbox")
            
            # Get CSRF token
            self.csrf_token = self.get_csrf_token()
            if not self.csrf_token:
                self.trigger_event('authentication_failed', {'reason': 'no_csrf_token'})
                return False
            
            # Prepare form data
            form_data = {
                '_token': self.csrf_token,
                'password': self.password
            }
            
            # Send unlock request
            response = self.session.post(
                self.unlock_url, 
                data=form_data, 
                timeout=30,
                allow_redirects=True
            )
            
            if response.status_code in [200, 302]:
                logger.info("Mailbox unlocked successfully")
                self.is_authenticated = True
                return True
            else:
                logger.error(f"Unlock failed with status: {response.status_code}")
                self.trigger_event('authentication_failed', {
                    'reason': 'unlock_failed',
                    'status_code': response.status_code
                })
                return False
                
        except Exception as e:
            logger.error(f"Error during unlock: {e}")
            self.trigger_event('error', {'type': 'unlock_error', 'error': str(e)})
            return False

    def parse_message(self, element, index: int) -> Optional[Message]:
        """Enhanced message parsing with priority detection"""
        try:
            content = element.get_text(strip=True)
            if not content or len(content) < 10:
                return None
            
            # Extract message details
            message_id = f"msg_{index}_{int(time.time())}"
            
            # Try to extract subject and sender
            subject = "No Subject"
            sender = "Unknown Sender"
            
            # Look for common email patterns
            subject_match = re.search(r'Subject:\s*(.+)', content, re.IGNORECASE)
            if subject_match:
                subject = subject_match.group(1).strip()
            
            sender_match = re.search(r'From:\s*(.+)', content, re.IGNORECASE)
            if sender_match:
                sender = sender_match.group(1).strip()
            
            # Determine priority based on keywords
            priority = MessagePriority.NORMAL
            urgent_keywords = ['urgent', 'important', 'asap', 'emergency']
            high_keywords = ['priority', 'attention', 'action required']
            
            content_lower = content.lower()
            if any(keyword in content_lower for keyword in urgent_keywords):
                priority = MessagePriority.URGENT
            elif any(keyword in content_lower for keyword in high_keywords):
                priority = MessagePriority.HIGH
            
            # Create message object
            message = Message(
                id=message_id,
                subject=subject,
                sender=sender,
                content=content[:500],  # Limit content length
                html_content=str(element)[:1000],
                timestamp=datetime.now(),
                priority=priority,
                tags=[]
            )
            
            return message
            
        except Exception as e:
            logger.error(f"Error parsing message: {e}")
            return None

    def apply_filters(self, messages: List[Message]) -> List[Message]:
        """Apply configured filters to messages"""
        if not self.filters:
            return messages
        
        filtered_messages = []
        
        for message in messages:
            passes_all_filters = True
            
            for filter_config in self.filters:
                # Check sender patterns
                if filter_config.sender_patterns:
                    if not any(re.search(pattern, message.sender, re.IGNORECASE) 
                             for pattern in filter_config.sender_patterns):
                        passes_all_filters = False
                        break
                
                # Check subject patterns
                if filter_config.subject_patterns:
                    if not any(re.search(pattern, message.subject, re.IGNORECASE) 
                             for pattern in filter_config.subject_patterns):
                        passes_all_filters = False
                        break
                
                # Check content patterns
                if filter_config.content_patterns:
                    if not any(re.search(pattern, message.content, re.IGNORECASE) 
                             for pattern in filter_config.content_patterns):
                        passes_all_filters = False
                        break
                
                # Check priority
                priority_values = {
                    MessagePriority.LOW: 1,
                    MessagePriority.NORMAL: 2,
                    MessagePriority.HIGH: 3,
                    MessagePriority.URGENT: 4
                }
                
                if priority_values[message.priority] < priority_values[filter_config.min_priority]:
                    passes_all_filters = False
                    break
                
                # Check age
                age_hours = (datetime.now() - message.timestamp).total_seconds() / 3600
                if age_hours > filter_config.max_age_hours:
                    passes_all_filters = False
                    break
                
                # Check read status
                if filter_config.exclude_read and message.is_read:
                    passes_all_filters = False
                    break
            
            if passes_all_filters:
                filtered_messages.append(message)
        
        logger.info(f"Filtered {len(messages)} messages to {len(filtered_messages)}")
        return filtered_messages

    def send_email_notification(self, message: Message):
        """Send email notification for new message"""
        if not self.notification_config.email_enabled:
            return
        
        try:
            # Create email
            msg = MimeMultipart()
            msg['From'] = self.notification_config.email_username
            msg['To'] = ', '.join(self.notification_config.email_recipients)
            msg['Subject'] = f"New Dujaw Message: {message.subject}"
            
            body = f"""
            New message received in Dujaw mailbox:
            
            From: {message.sender}
            Subject: {message.subject}
            Priority: {message.priority.value}
            Time: {message.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
            
            Content:
            {message.content}
            """
            
            msg.attach(MimeText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(
                self.notification_config.email_smtp_server,
                self.notification_config.email_smtp_port
            )
            server.starttls()
            server.login(
                self.notification_config.email_username,
                self.notification_config.email_password
            )
            
            text = msg.as_string()
            server.sendmail(
                self.notification_config.email_username,
                self.notification_config.email_recipients,
                text
            )
            server.quit()
            
            logger.info(f"Email notification sent for message: {message.id}")
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")

    def send_webhook_notification(self, message: Message):
        """Send webhook notification for new message"""
        if not self.notification_config.webhook_enabled:
            return
        
        try:
            payload = {
                'event': 'new_message',
                'timestamp': datetime.now().isoformat(),
                'message': asdict(message)
            }
            
            headers = self.notification_config.webhook_headers or {}
            headers['Content-Type'] = 'application/json'
            
            response = requests.post(
                self.notification_config.webhook_url,
                json=payload,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"Webhook notification sent for message: {message.id}")
            else:
                logger.warning(f"Webhook returned status {response.status_code}")
                
        except Exception as e:
            logger.error(f"Failed to send webhook notification: {e}")

    def start_monitoring(self, interval: int = 30):
        """Start background monitoring for new messages"""
        if self.monitoring_active:
            logger.warning("Monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitoring_thread.start()
        logger.info(f"Started background monitoring (interval: {interval}s)")

    def stop_monitoring(self):
        """Stop background monitoring"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("Stopped background monitoring")

    def _monitoring_loop(self, interval: int):
        """Background monitoring loop"""
        while self.monitoring_active:
            try:
                if self.is_authenticated:
                    new_messages = self.check_new_messages()
                    for message in new_messages:
                        self.trigger_event('new_message', message)
                        
                        # Send notifications
                        if self.notification_config.email_enabled:
                            self.send_email_notification(message)
                        
                        if self.notification_config.webhook_enabled:
                            self.send_webhook_notification(message)
                
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                self.trigger_event('error', {'type': 'monitoring_error', 'error': str(e)})
                time.sleep(interval)

    def check_new_messages(self) -> List[Message]:
        """Check for new messages since last check"""
        try:
            # Get current messages
            current_messages = self.get_messages()
            
            # Find new messages
            new_messages = []
            for message in current_messages:
                if message.id not in self.message_cache:
                    new_messages.append(message)
                    self.message_cache[message.id] = message
            
            if new_messages:
                logger.info(f"Found {len(new_messages)} new messages")
            
            return new_messages
            
        except Exception as e:
            logger.error(f"Error checking new messages: {e}")
            return []

    def get_messages(self, apply_filters: bool = True) -> List[Message]:
        """Get all messages with optional filtering"""
        try:
            if not self.is_authenticated:
                if not self.unlock_mailbox():
                    return []
            
            response = self.session.get(self.mailbox_url, timeout=30)
            
            if response.status_code != 200:
                logger.error(f"Failed to get mailbox: {response.status_code}")
                return []
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find message elements
            message_elements = soup.find_all(['div', 'tr', 'li'], class_=lambda x: x and any(
                keyword in x.lower() for keyword in ['message', 'mail', 'email', 'inbox']
            ))
            
            messages = []
            for i, element in enumerate(message_elements):
                message = self.parse_message(element, i)
                if message:
                    messages.append(message)
            
            # Apply filters if requested
            if apply_filters:
                messages = self.apply_filters(messages)
            
            logger.info(f"Retrieved {len(messages)} messages")
            return messages
            
        except Exception as e:
            logger.error(f"Error getting messages: {e}")
            self.trigger_event('error', {'type': 'get_messages_error', 'error': str(e)})
            return []

    def get_statistics(self) -> Dict[str, Any]:
        """Get detailed statistics about the mailbox"""
        messages = self.get_messages(apply_filters=False)
        
        stats = {
            'total_messages': len(messages),
            'unread_messages': sum(1 for m in messages if not m.is_read),
            'priority_breakdown': {
                'urgent': sum(1 for m in messages if m.priority == MessagePriority.URGENT),
                'high': sum(1 for m in messages if m.priority == MessagePriority.HIGH),
                'normal': sum(1 for m in messages if m.priority == MessagePriority.NORMAL),
                'low': sum(1 for m in messages if m.priority == MessagePriority.LOW)
            },
            'last_message_time': max([m.timestamp for m in messages], default=None),
            'cache_size': len(self.message_cache),
            'monitoring_active': self.monitoring_active,
            'filters_active': len(self.filters),
            'notifications_enabled': {
                'email': self.notification_config.email_enabled,
                'webhook': self.notification_config.webhook_enabled
            }
        }
        
        return stats
