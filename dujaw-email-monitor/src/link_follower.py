#!/usr/bin/env python3
"""
Seguidor automático de links de confirmación
Navega y procesa páginas de verificación
"""

import requests
import logging
import time
from typing import Optional, Dict, Any, List
from bs4 import BeautifulSoup

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config
from .utils import validate_url, sanitize_filename, generate_timestamp


class LinkFollower:
    """Seguidor automático de links"""
    
    def __init__(self, confirmation_password: str = None):
        """
        Inicializa el seguidor de links
        
        Args:
            confirmation_password: Password para confirmaciones (opcional)
        """
        self.logger = logging.getLogger('dujaw_monitor.follower')
        self.confirmation_password = confirmation_password or config.DEFAULT_CONFIRMATION_PASSWORD
        
        # Configurar sesión HTTP
        self.session = requests.Session()
        self.session.headers.update(config.DEFAULT_HEADERS)
        
        self.logger.info("Link Follower inicializado")
    
    def follow_link(self, link: str, save_response: bool = True) -> Optional[Dict[str, Any]]:
        """
        Sigue un link y analiza la respuesta
        
        Args:
            link: Link a seguir
            save_response: Si guardar la respuesta HTML
        
        Returns:
            Diccionario con información de la respuesta o None si hay error
        """
        if not validate_url(link):
            self.logger.error(f"URL inválida: {link}")
            return None
        
        try:
            self.logger.info(f"Siguiendo link: {link}")
            
            # Hacer request al link
            response = self.session.get(link, allow_redirects=True, timeout=config.HTTP_TIMEOUT)
            
            self.logger.info(f"Respuesta: {response.status_code} - {response.url}")
            
            if response.status_code != 200:
                self.logger.warning(f"Status code no exitoso: {response.status_code}")
            
            # Analizar respuesta
            analysis = self._analyze_response(response, link)
            
            # Guardar respuesta si se solicita
            if save_response and config.SAVE_HTTP_RESPONSES:
                self._save_response(response, link)
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error siguiendo link {link}: {e}")
            return None
    
    def _analyze_response(self, response: requests.Response, original_link: str) -> Dict[str, Any]:
        """
        Analiza la respuesta HTTP
        
        Args:
            response: Respuesta HTTP
            original_link: Link original solicitado
        
        Returns:
            Diccionario con análisis de la respuesta
        """
        analysis = {
            'original_link': original_link,
            'final_url': response.url,
            'status_code': response.status_code,
            'content_length': len(response.text),
            'content_type': response.headers.get('content-type', ''),
            'is_redirect': response.url != original_link,
            'page_analysis': {},
            'forms': [],
            'confirmation_detected': False,
            'error_detected': False,
            'success_detected': False
        }
        
        try:
            # Analizar contenido HTML
            if 'text/html' in analysis['content_type']:
                soup = BeautifulSoup(response.text, 'html.parser')
                analysis['page_analysis'] = self._analyze_html_page(soup)
                analysis['forms'] = self._extract_forms(soup)
                
                # Detectar tipos de página
                analysis['confirmation_detected'] = self._detect_confirmation_page(soup, response.text)
                analysis['error_detected'] = self._detect_error_page(soup, response.text)
                analysis['success_detected'] = self._detect_success_page(soup, response.text)
            
        except Exception as e:
            self.logger.error(f"Error analizando respuesta: {e}")
        
        return analysis
    
    def _analyze_html_page(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """
        Analiza una página HTML
        
        Args:
            soup: Objeto BeautifulSoup
        
        Returns:
            Diccionario con análisis de la página
        """
        analysis = {
            'title': '',
            'headings': [],
            'buttons': [],
            'links': [],
            'input_fields': [],
            'text_content': ''
        }
        
        try:
            # Título
            title_tag = soup.find('title')
            if title_tag:
                analysis['title'] = title_tag.get_text(strip=True)
            
            # Encabezados
            for heading in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                analysis['headings'].append({
                    'level': heading.name,
                    'text': heading.get_text(strip=True)
                })
            
            # Botones
            for button in soup.find_all(['button', 'input'], type=['button', 'submit']):
                button_text = button.get_text(strip=True) or button.get('value', '')
                if button_text:
                    analysis['buttons'].append({
                        'text': button_text,
                        'type': button.name,
                        'id': button.get('id', ''),
                        'class': button.get('class', [])
                    })
            
            # Links importantes
            for link in soup.find_all('a', href=True):
                link_text = link.get_text(strip=True)
                if link_text and any(keyword in link_text.lower() for keyword in 
                                   ['approve', 'confirm', 'verify', 'activate', 'reject', 'cancel']):
                    analysis['links'].append({
                        'text': link_text,
                        'href': link['href'],
                        'class': link.get('class', [])
                    })
            
            # Campos de entrada
            for input_field in soup.find_all('input'):
                field_type = input_field.get('type', 'text')
                field_name = input_field.get('name', '')
                
                if field_name:
                    analysis['input_fields'].append({
                        'name': field_name,
                        'type': field_type,
                        'value': input_field.get('value', ''),
                        'placeholder': input_field.get('placeholder', ''),
                        'required': input_field.has_attr('required')
                    })
            
            # Contenido de texto principal
            main_content = soup.find(['main', 'div'], class_=lambda x: x and any(
                keyword in ' '.join(x).lower() for keyword in ['content', 'main', 'body']
            ))
            
            if main_content:
                analysis['text_content'] = main_content.get_text(strip=True)[:500]
            else:
                analysis['text_content'] = soup.get_text(strip=True)[:500]
            
        except Exception as e:
            self.logger.error(f"Error analizando página HTML: {e}")
        
        return analysis
    
    def _extract_forms(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        Extrae información de formularios
        
        Args:
            soup: Objeto BeautifulSoup
        
        Returns:
            Lista de formularios encontrados
        """
        forms = []
        
        try:
            for form in soup.find_all('form'):
                form_info = {
                    'action': form.get('action', ''),
                    'method': form.get('method', 'GET').upper(),
                    'inputs': [],
                    'has_password': False,
                    'has_email': False,
                    'is_confirmation_form': False
                }
                
                # Analizar inputs del formulario
                for input_elem in form.find_all(['input', 'select', 'textarea']):
                    input_info = {
                        'type': input_elem.get('type', input_elem.name),
                        'name': input_elem.get('name', ''),
                        'value': input_elem.get('value', ''),
                        'placeholder': input_elem.get('placeholder', ''),
                        'required': input_elem.has_attr('required'),
                        'readonly': input_elem.has_attr('readonly')
                    }
                    
                    form_info['inputs'].append(input_info)
                    
                    # Detectar tipos especiales
                    if input_info['type'] == 'password':
                        form_info['has_password'] = True
                    elif input_info['type'] == 'email' or 'email' in input_info['name'].lower():
                        form_info['has_email'] = True
                
                # Detectar si es formulario de confirmación
                form_text = form.get_text(strip=True).lower()
                if any(keyword in form_text for keyword in 
                       ['approve', 'confirm', 'verify', 'activate']):
                    form_info['is_confirmation_form'] = True
                
                forms.append(form_info)
                
        except Exception as e:
            self.logger.error(f"Error extrayendo formularios: {e}")
        
        return forms
    
    def _detect_confirmation_page(self, soup: BeautifulSoup, html_content: str) -> bool:
        """
        Detecta si es una página de confirmación
        
        Args:
            soup: Objeto BeautifulSoup
            html_content: Contenido HTML completo
        
        Returns:
            True si es página de confirmación
        """
        confirmation_indicators = [
            'approve your email change',
            'email change approval',
            'confirm your email',
            'verify your email',
            'activate your account',
            'email verification'
        ]
        
        html_lower = html_content.lower()
        
        return any(indicator in html_lower for indicator in confirmation_indicators)
    
    def _detect_error_page(self, soup: BeautifulSoup, html_content: str) -> bool:
        """
        Detecta si es una página de error
        
        Args:
            soup: Objeto BeautifulSoup
            html_content: Contenido HTML completo
        
        Returns:
            True si es página de error
        """
        error_indicators = [
            'invalid or has timed out',
            'link has expired',
            'error occurred',
            'not found',
            'access denied',
            'invalid request'
        ]
        
        html_lower = html_content.lower()
        
        return any(indicator in html_lower for indicator in error_indicators)
    
    def _detect_success_page(self, soup: BeautifulSoup, html_content: str) -> bool:
        """
        Detecta si es una página de éxito

        Args:
            soup: Objeto BeautifulSoup
            html_content: Contenido HTML completo

        Returns:
            True si es página de éxito
        """
        success_indicators = [
            'successfully confirmed',
            'email has been verified',
            'confirmation successful',
            'account activated',
            'email updated',
            'change approved',
            'email has been changed',
            'change complete',
            'verification successful'
        ]

        html_lower = html_content.lower()

        return any(indicator in html_lower for indicator in success_indicators)
    
    def _save_response(self, response: requests.Response, original_link: str) -> str:
        """
        Guarda la respuesta HTTP en un archivo
        
        Args:
            response: Respuesta HTTP
            original_link: Link original
        
        Returns:
            Ruta del archivo guardado
        """
        try:
            timestamp = generate_timestamp()
            domain = original_link.split('/')[2] if '/' in original_link else 'unknown'
            filename = f"response_{sanitize_filename(domain)}_{timestamp}.html"
            filepath = os.path.join(config.OUTPUT_DIR, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"<!-- Original Link: {original_link} -->\n")
                f.write(f"<!-- Final URL: {response.url} -->\n")
                f.write(f"<!-- Status Code: {response.status_code} -->\n")
                f.write(f"<!-- Timestamp: {timestamp} -->\n\n")
                f.write(response.text)
            
            self.logger.debug(f"Respuesta guardada: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error guardando respuesta: {e}")
            return ""
    
    def attempt_confirmation(self, analysis: Dict[str, Any], email: str = None) -> Optional[Dict[str, Any]]:
        """
        Intenta realizar confirmación automática si es posible
        
        Args:
            analysis: Análisis de la página
            email: Email para usar en formularios (opcional)
        
        Returns:
            Resultado de la confirmación o None si no es posible
        """
        if not analysis['confirmation_detected']:
            self.logger.info("No es una página de confirmación")
            return None
        
        # Buscar formulario de confirmación
        confirmation_form = None
        for form in analysis['forms']:
            if form['is_confirmation_form'] and form['has_password']:
                confirmation_form = form
                break
        
        if not confirmation_form:
            self.logger.info("No se encontró formulario de confirmación válido")
            return None
        
        try:
            self.logger.info("Intentando confirmación automática...")
            
            # Preparar datos del formulario
            form_data = {}
            
            for input_info in confirmation_form['inputs']:
                name = input_info['name']
                input_type = input_info['type']
                
                if input_type == 'password':
                    form_data[name] = self.confirmation_password
                elif input_type == 'hidden':
                    form_data[name] = input_info['value']
                elif input_type in ['text', 'email'] and email:
                    if 'email' in name.lower():
                        form_data[name] = email
                    else:
                        form_data[name] = input_info['value'] or ''
                else:
                    form_data[name] = input_info['value'] or ''
            
            # Determinar URL de envío
            action = confirmation_form['action']
            if action.startswith('http'):
                submit_url = action
            elif action.startswith('/'):
                submit_url = f"https://{analysis['final_url'].split('/')[2]}{action}"
            elif action and action.strip():
                submit_url = f"{analysis['final_url'].rsplit('/', 1)[0]}/{action}"
            else:
                # Si no hay action o está vacío, usar la URL original
                submit_url = analysis['final_url']
            
            self.logger.info(f"Enviando confirmación a: {submit_url}")
            
            # Enviar formulario
            method = confirmation_form['method']
            
            if method == 'POST':
                response = self.session.post(submit_url, data=form_data, allow_redirects=True)
            else:
                response = self.session.get(submit_url, params=form_data, allow_redirects=True)
            
            # Analizar respuesta de confirmación
            confirmation_result = self._analyze_response(response, submit_url)

            # Detectar éxito por redirección (específico para Pokémon)
            original_domain = analysis['final_url'].split('/')[2] if '/' in analysis['final_url'] else ''
            final_domain = response.url.split('/')[2] if '/' in response.url else ''

            # Si hay redirección a un dominio diferente, es probable que sea éxito
            if (original_domain != final_domain and
                'pokemon.com' in original_domain and
                'pokemon.com' in final_domain and
                response.status_code == 200):

                self.logger.info(f"Redirección detectada: {analysis['final_url']} -> {response.url}")
                confirmation_result['success_detected'] = True
                confirmation_result['redirect_detected'] = True

            # También detectar si el formulario de password desapareció
            elif response.status_code == 200:
                try:
                    response_soup = BeautifulSoup(response.text, 'html.parser')
                    has_password_form = bool(response_soup.find('input', {'type': 'password'}))

                    if not has_password_form and not confirmation_result['error_detected']:
                        self.logger.info("Formulario de password desapareció - posible éxito")
                        confirmation_result['success_detected'] = True
                        confirmation_result['form_disappeared'] = True
                except Exception as e:
                    self.logger.debug(f"Error verificando formulario: {e}")

            self.logger.info(f"Confirmación enviada - Status: {response.status_code}")

            if confirmation_result['success_detected']:
                self.logger.info("✅ Confirmación exitosa detectada")
            elif confirmation_result['error_detected']:
                self.logger.warning("⚠️ Error en confirmación detectado")
            else:
                self.logger.warning("⚠️ Confirmación enviada pero resultado incierto")

            return confirmation_result
            
        except Exception as e:
            self.logger.error(f"Error en confirmación automática: {e}")
            return None
    
    def close(self) -> None:
        """Cierra la sesión HTTP"""
        try:
            self.session.close()
            self.logger.info("Sesión HTTP cerrada")
        except Exception as e:
            self.logger.warning(f"Error cerrando sesión: {e}")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()
